.smart-dashboard-container {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  height: 100%; // Take full available height from parent dashboard
  overflow: hidden; // Prevent outer scrollbar, let internal areas handle scrolling

  // ===== MATERIAL DESIGN THEME OVERRIDES =====
  ::ng-deep {
    // Form fields
    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        height: 36px;
        min-height: 36px;
      }

      .mat-mdc-form-field-infix {
        padding: 6px 12px;
        min-height: 24px;
        border-top: none;
      }

      .mat-mdc-form-field-flex {
        align-items: center;
        height: 36px;
      }

      .mat-mdc-form-field-subscript-wrapper {
        display: none;
      }

      .mat-mdc-form-field-outline {
        color: #dee2e6;
      }

      .mat-mdc-form-field-outline-thick {
        color: #ffb366;
      }

      .mat-mdc-form-field-label {
        color: #666;
        font-size: 13px;
        top: 50%;
        transform: translateY(-50%);
        line-height: 1;
        transition: all 0.2s ease;
      }

      &.mat-focused .mat-mdc-form-field-label {
        color: #ffb366;
      }

      &.mat-form-field-should-float .mat-mdc-form-field-label {
        transform: translateY(-28px) scale(0.75);
        top: 0;
      }
    }

    // Select dropdowns
    .mat-mdc-select {
      .mat-mdc-select-trigger {
        height: 36px;
        display: flex;
        align-items: center;
      }

      .mat-mdc-select-value {
        font-size: 13px;
        line-height: 24px;
      }

      .mat-mdc-select-arrow {
        color: #ffb366;
      }
    }

    // Select panel
    .mat-mdc-select-panel .mat-mdc-option {
      height: 32px;
      line-height: 32px;
      font-size: 13px;
      padding: 0 16px;

      &.mat-mdc-option-active {
        background: rgba(255, 179, 102, 0.1);
        color: #ffb366;
      }

      &:hover {
        background: rgba(255, 179, 102, 0.05);
      }
    }

    // Input elements
    .mat-mdc-input-element {
      font-size: 13px;
      height: 24px;
      line-height: 24px;
    }

    // Date picker
    .mat-datepicker-toggle .mat-icon {
      color: #ffb366;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .mat-datepicker-content {
      .mat-calendar-header {
        background: #ffb366;
        color: white;
      }

      .mat-calendar-body-selected {
        background-color: #ffb366;
        color: white;
      }

      .mat-calendar-body-today:not(.mat-calendar-body-selected) {
        border-color: #ffb366;
      }
    }

    // Buttons
    .mat-mdc-raised-button,
    .mat-mdc-outlined-button {
      height: 32px;
      line-height: 32px;
      padding: 0 12px;
      font-size: 13px;

      &.mat-primary {
        background-color: #ffb366;
        color: white;

        &:hover {
          background-color: #ffa64d;
        }
      }
    }

    .mat-mdc-outlined-button.mat-primary {
      border-color: #ffb366;
      color: #ffb366;
      background-color: transparent;

      &:hover {
        background-color: rgba(255, 179, 102, 0.05);
      }
    }
  }

  // Main Layout Container
  .main-layout {
    display: flex;
    height: 100%; // Use full available height from parent container
    overflow: hidden; // Prevent outer scrollbar

    // Left Sidebar
    .left-sidebar {
      width: 250px;
      background: linear-gradient(135deg, #ffffff 0%, #fff5f0 100%);
      border-right: 1px solid #ffe0cc;
      padding: 12px 12px 60px 12px; // Add bottom padding for toggle switch
      box-shadow: 2px 0 4px rgba(255, 179, 102, 0.08);
      height: 100%; // Take full available height
      overflow-y: auto; // Allow scrolling within sidebar if needed
      position: relative; // For absolute positioned toggle switch

      // Custom scrollbar styling - default gray colors
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;

        &:hover {
          background: rgba(0, 0, 0, 0.3);
        }
      }

      // Dashboard Selection
      .dashboard-selection {
        margin-bottom: 5px;

        .dashboard-dropdown {
          width: 100%;

          ::ng-deep .mat-mdc-text-field-wrapper {
            background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
            border-radius: 6px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

            &:hover {
              border-color: #ffb366;
              box-shadow: 0 2px 6px rgba(255, 179, 102, 0.1);
            }
          }

          ::ng-deep .mat-mdc-form-field-outline,
          ::ng-deep .mat-mdc-form-field-outline-thick {
            display: none;
          }

          ::ng-deep .mat-focused .mat-mdc-text-field-wrapper {
            border-color: #ffb366;
            box-shadow: 0 3px 8px rgba(255, 179, 102, 0.15);
          }
        }
      }

      // Filters Section
      .filters-section {
        .filters-title {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #333;

          mat-icon {
            color: #ffb366;
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }

        .filter-group {
          display: flex;
          flex-direction: column;
          align-items: stretch;
          padding-bottom: 4px;

          .filter-label {
            font-size: 13px;
            font-weight: 500;
            color: #333;
          }

          .filter-field {
            width: 100%;

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
            }
          }
        }

        .date-range-group {
          display: flex;
          flex-direction: column;
        }



        .filter-actions {
          margin-top: 20px;
          border-top: 1px solid #e9ecef;
          display: flex;
          gap: 8px;

          .search-btn,
          .reset-filters-btn {
            flex: 1;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          .search-btn {
            background-color: white;
            color: #ffb366;
            border: 2px solid #ffb366;
            font-weight: 600;

            &:hover {
              background-color: #fff8f5;
              border-color: #ffa64d;
              color: #ffa64d;
            }
          }

          .reset-filters-btn {
            color: #6c757d;
            border-color: #dee2e6;

            &:hover {
              background-color: #f8f9fa;
              border-color: #adb5bd;
            }
          }
        }
      }
    }

    // Dashboard Mode Toggle Section
    .dashboard-mode-section {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
      border-top: 2px solid #ffb366;
      padding: 8px;
      box-shadow: 0 -2px 8px rgba(255, 179, 102, 0.15);

      .mode-label {
        font-size: 13px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 8px;

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
          color: #ffb366;
        }
      }

      .mode-toggle-container {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .toggle-switch {
          display: flex;
          background: #f8f9fa;
          border: 2px solid #e9ecef;
          border-radius: 12px;
          padding: 4px;
          position: relative;
          overflow: hidden;

          .toggle-option {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;

            &.active {
              background: #ff8c42;
              color: white;
              box-shadow: 0 2px 8px rgba(255, 140, 66, 0.3);
            }

            &:not(.active) {
              color: #6c757d;
              background: transparent;

              &:hover:not(.disabled) {
                background: rgba(255, 140, 66, 0.1);
                color: #ff8c42;
              }
            }

            &.disabled {
              cursor: not-allowed;
              opacity: 0.6;

              &:hover {
                background: transparent !important;
                color: #6c757d !important;
              }
            }

            .beta-icon {
              font-size: 12px;
              width: 12px;
              height: 12px;
            }
          }

          &.ai-disabled .ai-option {
            position: relative;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(173, 181, 189, 0.1) 2px,
                rgba(173, 181, 189, 0.1) 4px
              );
              pointer-events: none;
            }
          }
        }

        .mode-description {
          text-align: center;
          margin-top: 4px;

          .coming-soon {
            font-size: 10px;
            color: #adb5bd;
            font-style: italic;
            font-weight: 500;
          }
        }
      }
    }

    // Right Content Area
    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #f8f9fa;
      height: 100%; // Take full available height
      overflow: hidden; // Prevent outer scrollbar

      // AI Assistant Header
      .ai-assistant-header {
        background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
        border-bottom: 1px solid #e9ecef;
        padding: 10px 20px;
        display: flex;
        align-items: center;
        gap: 16px;
        min-height: 50px;
        box-shadow: 0 1px 3px rgba(255, 179, 102, 0.05);

        .assistant-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;

          .assistant-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
            transition: color 0.3s ease;

            &.disabled {
              color: #adb5bd;
            }
          }

          .assistant-text {
            display: flex;
            align-items: center;
            gap: 10px;

            .assistant-title {
              font-size: 14px;
              font-weight: 600;
              color: #333;
              white-space: nowrap;
            }

            .assistant-status {
              font-size: 10px;
              font-weight: 500;
              padding: 2px 8px;
              border-radius: 12px;
              display: inline-block;
              white-space: nowrap;

              &.disabled {
                color: #adb5bd;
                background: #f8f9fa;
              }
            }
          }
        }

        .search-container {
          flex: 1;
          min-width: 0;
          max-width: calc(100% - 200px);

          .search-field {
            width: 100%;

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
              border-radius: 24px;
              height: 36px;
              border: 1px solid #e9ecef;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
              transition: all 0.3s ease;

              &:hover:not(.disabled) {
                border-color: #ffb366;
                box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);
              }
            }

            ::ng-deep .mat-mdc-form-field-outline,
            ::ng-deep .mat-mdc-form-field-outline-thick {
              display: none;
            }

            ::ng-deep .mat-mdc-form-field-infix {
              padding: 6px 16px;
              border-top: none;
            }

            ::ng-deep .mat-mdc-form-field-flex {
              align-items: center;
              height: 36px;
            }

            ::ng-deep input {
              font-size: 14px;
              color: #333;

              &::placeholder {
                color: #999;
                font-size: 14px;
              }
            }

            ::ng-deep .mat-focused .mat-mdc-text-field-wrapper {
              border-color: #ffb366;
              box-shadow: 0 4px 16px rgba(255, 179, 102, 0.15);
            }

            .search-icon {
              color: #999;
              cursor: pointer;
              font-size: 16px;
              transition: color 0.2s ease;

              &:hover:not(.disabled) {
                color: #ffb366;
              }

              &.disabled {
                color: #adb5bd;
                cursor: not-allowed;
              }
            }

            &.disabled {
              ::ng-deep .mat-mdc-text-field-wrapper {
                background-color: #f8f9fa;
                border-color: #e9ecef;
                box-shadow: none;

                &:hover {
                  border-color: #e9ecef;
                  box-shadow: none;
                }
              }

              ::ng-deep input {
                color: #adb5bd;
                cursor: not-allowed;
              }

              ::ng-deep input::placeholder {
                color: #adb5bd;
              }
            }
          }
        }
      }

      // Dashboard Content Area
      .dashboard-content-area {
        padding: 12px;
        flex: 1; // Take remaining height after search header
        overflow-y: auto; // Only this area should scroll
        background: #fafbfc;

        // Custom scrollbar styling - default gray colors
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;

          &:hover {
            background: rgba(0, 0, 0, 0.3);
          }
        }

        .loading-container {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 500px;
          padding: 40px 20px;
          background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
          border-radius: 16px;
          border: 1px solid #ffe0cc;
          margin: 20px;
          box-shadow: 0 8px 32px rgba(255, 179, 102, 0.12);

          .loading-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 32px;
            max-width: 400px;
            text-align: center;

            .loading-animation {
              position: relative;
              width: 120px;
              height: 120px;
              display: flex;
              align-items: center;
              justify-content: center;

              .pulse-circle {
                position: absolute;
                width: 100%;
                height: 100%;
                border: 3px solid #ffb366;
                border-radius: 50%;
                opacity: 0;
                animation: pulse 2s ease-in-out infinite;

                &.delay-1 {
                  animation-delay: 0.5s;
                }

                &.delay-2 {
                  animation-delay: 1s;
                }
              }

              .loading-icon {
                font-size: 48px;
                width: 48px;
                height: 48px;
                color: #ffb366;
                z-index: 1;
                animation: float 3s ease-in-out infinite;
              }
            }

            .loading-text {
              h3 {
                margin: 0 0 8px 0;
                font-size: 24px;
                font-weight: 600;
                color: #333;
                background: linear-gradient(135deg, #ffb366 0%, #ffc999 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
              }

              p {
                margin: 0;
                font-size: 16px;
                color: #666;
                line-height: 1.5;
              }
            }
          }
        }

        // Loading Animations
        @keyframes pulse {
          0% {
            transform: scale(0.8);
            opacity: 1;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.3;
          }
          100% {
            transform: scale(1.4);
            opacity: 0;
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        .dashboard-grid {
          // Add section headers for better organization
          .dashboard-section {
            margin-bottom: 20px;

            .section-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 12px;
              padding: 6px 0;
              border-bottom: 1px solid #e9ecef;

              .section-icon {
                width: 20px;
                height: 20px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #ff8c42;

                mat-icon {
                  font-size: 14px;
                  width: 14px;
                  height: 14px;
                  color: white;
                }
              }

              .section-title {
                font-size: 14px;
                font-weight: 600;
                color: #333;
                margin: 0;
              }

              .section-subtitle {
                font-size: 11px;
                color: #6c757d;
                font-weight: 400;
                margin-left: auto;
              }
            }
          }

          .summary-cards-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 12px;
            margin-bottom: 16px;

            .summary-card {
              background: white;
              border-radius: 8px;
              border: 1px solid #e9ecef;
              transition: box-shadow 0.2s ease;
              position: relative;
              overflow: hidden;

              &:hover {
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
              }



              .card-content {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 16px;

                .card-icon {
                  width: 36px;
                  height: 36px;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  flex-shrink: 0;
                  background: linear-gradient(135deg, #ffb366 0%, #ff8c42 100%);

                  mat-icon {
                    font-size: 18px;
                    width: 18px;
                    height: 18px;
                    color: white;
                  }
                }

                .card-info {
                  flex: 1;
                  min-width: 0;

                  .card-value {
                    font-size: 20px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4px;
                    line-height: 1.2;
                    word-break: break-word;
                  }

                  .card-label {
                    font-size: 12px;
                    color: #6c757d;
                    font-weight: 500;
                    line-height: 1.3;
                  }
                }
              }
            }
          }

          .charts-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 12px;

            .chart-card {
              background: white;
              border-radius: 8px;
              border: 1px solid #e1e8ed;
              transition: box-shadow 0.2s ease;
              height: 380px;
              overflow: hidden;
              position: relative;

              &:hover {
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
              }

              // Enhanced width classes with better space utilization
              &.full-width {
                grid-column: span 12;
              }

              &.half-width {
                grid-column: span 6;
              }

              &.third-width {
                grid-column: span 4;
              }

              &.quarter-width {
                grid-column: span 3;
              }

              &.two-thirds-width {
                grid-column: span 8;
              }



              // Special styling for table charts
              &:has([data-chart-type="table"]) {
                height: auto;
                min-height: auto;

                .chart-container {
                  height: auto;
                  overflow: visible;
                }
              }

              .chart-title {
                font-size: 14px;
                font-weight: 600;
                color: #333;
                margin: 0;
                padding: 12px 16px 8px 16px;
                border-bottom: 1px solid #f1f3f4;
                background: #fafbfc;
              }

              .chart-container {
                position: relative;
                height: 320px;
                padding: 16px;

                // Table charts need auto height and full width
                &[data-chart-type="table"] {
                  height: auto;
                  overflow: visible;
                  padding: 0;
                  width: 100%;

                  .table-chart {
                    width: 100%;
                    min-width: 100%;
                    overflow: visible;
                  }
                }

                canvas {
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }
        }

        // ===== ENHANCED TABLE STYLING =====
        .table-chart {
          width: 100%;
          margin: 0;

          .table-responsive {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            width: 100%;
            max-width: none;
            overflow-x: auto;
            overflow-y: visible;

            // Table Controls Section
            .table-controls {
              background: white;
              border-bottom: 1px solid #e2e8f0;
              padding: 16px 4px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              gap: 20px;
              min-height: 64px;
              border-radius: 12px 12px 0 0;

              .table-search {
                flex: 1;
                max-width: 400px;

                .search-field {
                  width: 100%;

                  ::ng-deep .mat-mdc-text-field-wrapper {
                    background-color: white;
                    border-radius: 8px;
                    height: 40px;
                    border: 2px solid #e2e8f0;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;

                    &:hover {
                      border-color: #ffb366;
                      box-shadow: 0 2px 4px rgba(255, 179, 102, 0.1);
                    }
                  }

                  ::ng-deep .mat-mdc-form-field-outline,
                  ::ng-deep .mat-mdc-form-field-outline-thick {
                    display: none;
                  }

                  ::ng-deep .mat-focused .mat-mdc-text-field-wrapper {
                    border-color: #ffb366 !important;
                    box-shadow: 0 0 0 3px rgba(255, 179, 102, 0.1) !important;
                  }

                  ::ng-deep .mat-mdc-form-field-infix {
                    padding: 8px 16px;
                    border-top: none;
                    display: flex;
                    align-items: center;
                    min-height: 24px;
                  }

                  ::ng-deep .mat-mdc-form-field-flex {
                    align-items: center;
                    height: 40px;
                  }

                  ::ng-deep .mat-mdc-form-field-label {
                    color: #718096 !important;
                    font-size: 14px !important;
                    top: 50% !important;
                    transform: translateY(-50%) !important;
                    line-height: 1 !important;
                  }

                  ::ng-deep .mat-form-field-should-float .mat-mdc-form-field-label {
                    transform: translateY(-28px) scale(0.75) !important;
                  }

                  ::ng-deep input {
                    font-size: 14px !important;
                    color: #2d3748 !important;
                    font-weight: 500 !important;
                    line-height: 24px !important;
                    height: 24px !important;
                    padding: 0 !important;

                    &::placeholder {
                      color: #718096 !important;
                      font-size: 14px !important;
                      font-weight: 400 !important;
                      line-height: 24px !important;
                    }
                  }

                  ::ng-deep .mat-icon {
                    color: #ffb366 !important;
                    font-size: 18px !important;
                    width: 18px !important;
                    height: 18px !important;
                  }
                }
              }

              .table-export {
                button {
                  background: white !important;
                  color: #ffb366 !important;
                  border: 1px solid #ffb366 !important;
                  // padding: 10px 18px;
                  font-weight: 600;
                  transition: all 0.2s ease;
                  box-shadow: 0 2px 4px rgba(255, 179, 102, 0.1);
                  text-transform: uppercase;
                  // letter-spacing: 0.3px;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  &:hover {
                    background: #fff8f5 !important;
                    color: #ff9f4a !important;
                    border-color: #ff9f4a !important;
                    transform: translateY(-1px);
                    box-shadow: 0 3px 6px rgba(255, 179, 102, 0.2);
                  }

                  &:active {
                    transform: translateY(0);
                    box-shadow: 0 1px 3px rgba(255, 179, 102, 0.2);
                  }

                  mat-icon {
                    font-size: 16px;
                    width: 16px;
                    height: 16px;
                    margin-right: 6px;
                  }
                }
              }
            }

            // Enhanced Material Table Styling
            .reconciliation-mat-table {
              width: 100%;
              min-width: 100%; // Use full available width
              background: white;
              border-collapse: separate;
              border-spacing: 0;
              table-layout: auto; // Auto layout for better space utilization
              overflow: visible;
              word-wrap: break-word;

              // Header Styling
              .mat-mdc-header-row {
                background: #f8f9fa !important;
                border: none;
                height: auto !important;
                min-height: 40px !important;
                border-bottom: 1px solid #dee2e6;

                .mat-mdc-header-cell {
                  background: #f8f9fa !important;
                  color: #495057 !important;
                  font-weight: 600 !important;
                  font-size: 11px !important;
                  text-transform: uppercase !important;
                  letter-spacing: 0.2px !important;
                  padding: 10px 6px !important;
                  border: none !important;
                  border-right: 1px solid #dee2e6 !important;
                  text-align: center !important;
                  vertical-align: top !important;
                  white-space: normal !important;
                  overflow: visible !important;
                  text-overflow: clip !important;
                  height: auto !important;
                  min-height: 40px !important;
                  line-height: 1.2 !important;
                  word-wrap: break-word !important;
                  hyphens: auto !important;
                  display: table-cell !important;

                  &:last-child {
                    border-right: none !important;
                  }

                  &:first-child {
                    text-align: left !important;
                    padding-left: 14px !important;
                    padding-right: 6px !important;
                    width: 20% !important;
                    min-width: 180px !important;
                    color: #343a40 !important;
                    font-weight: 700 !important;
                  }

                  // Distribute remaining space evenly among data columns
                  &:not(:first-child) {
                    width: auto !important;
                    min-width: 100px !important;
                    text-align: center !important;
                  }
                }
              }

              // Row Styling
              .mat-mdc-row {
                border: none;
                height: 44px;
                transition: all 0.2s ease;
                background: white;
                border-bottom: 1px solid #f7fafc;

                &:nth-child(even) {
                  background: #fafbfc;
                }



                .mat-mdc-cell {
                  padding: 10px 6px !important;
                  border: none !important;
                  border-right: 1px solid #dee2e6 !important;
                  font-size: 12px !important;
                  color: #495057 !important;
                  text-align: center !important;
                  vertical-align: middle !important;
                  position: relative;
                  white-space: nowrap;
                  overflow: visible;
                  text-overflow: ellipsis;
                  font-weight: 400 !important;
                  display: table-cell !important;
                  height: 40px !important;
                  line-height: 1.2 !important;

                  &:last-child {
                    border-right: none !important;
                  }

                  &:first-child {
                    text-align: left !important;
                    padding-left: 14px !important;
                    padding-right: 6px !important;
                    font-weight: 600 !important;
                    color: #343a40 !important;
                    width: 20% !important;
                    min-width: 180px !important;
                  }

                  &:not(:first-child) {
                    width: auto !important;
                    min-width: 100px !important;
                    text-align: center !important;
                  }

                  // Empty cell styling
                  .empty-cell {
                    color: #a0aec0 !important;
                    font-style: italic !important;
                    font-weight: 400 !important;
                  }

                  // Number formatting
                  &:not(:first-child) {
                    font-family: 'Roboto', 'Arial', sans-serif !important;
                    font-weight: 600 !important;
                    font-size: 14px !important;
                  }

                  // Ensure proper alignment for all content
                  span {
                    display: inline-block;
                    width: 100%;
                    text-align: inherit;
                    vertical-align: middle;
                    line-height: 1.2;
                  }
                }

                // Special row types
                &.category-row {
                  background: #f7fafc;
                  border-top: 1px solid #cbd5e0;
                  border-bottom: 1px solid #cbd5e0;

                  .mat-mdc-cell {
                    font-weight: 600;
                    color: #4a5568;

                    &:first-child {
                      color: #2d3748;
                      font-size: 13px;
                    }
                  }


                }

                &.subcategory-row {
                  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                  border-left: 3px solid #ffb366;

                  .mat-mdc-cell {
                    &:first-child {
                      padding-left: 32px;
                      font-weight: 500;
                      color: #495057;
                    }
                  }
                }

                &.grand-total-row {
                  background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
                  border-top: 2px solid #007bff;
                  border-bottom: 2px solid #007bff;
                  font-weight: 700;

                  .mat-mdc-cell {
                    color: #0056b3;
                    font-size: 14px;
                    font-weight: 700;

                    &:first-child {
                      color: #003d82;
                      text-transform: uppercase;
                      letter-spacing: 0.5px;
                    }
                  }


                }
              }
            }

            // Pagination Styling
            .mat-mdc-paginator {
              background: #fafbfc;
              border-top: 1px solid #e2e8f0;
              border-radius: 0 0 12px 12px;

              .mat-mdc-paginator-container {
                padding: 12px 20px;
                min-height: 48px;
                display: flex;
                align-items: center;
                justify-content: space-between;
              }

              .mat-mdc-paginator-page-size,
              .mat-mdc-paginator-range-label {
                font-size: 13px;
                color: #4a5568;
                font-weight: 500;
              }

              .mat-mdc-icon-button {
                width: 32px;
                height: 32px;
                color: #718096;
                border-radius: 6px;
                transition: all 0.2s ease;

                &:hover {
                  background: #f7fafc;
                  color: #4a5568;
                }

                &[disabled] {
                  color: #cbd5e0;
                }

                mat-icon {
                  font-size: 16px;
                  width: 16px;
                  height: 16px;
                }
              }

              .mat-mdc-select {
                .mat-mdc-select-trigger {
                  height: 32px;
                  font-size: 12px;
                  font-weight: 500;
                  border-radius: 4px;
                  border: 1px solid #e2e8f0;
                  background: white;
                  padding: 0 10px;
                }

                .mat-mdc-select-arrow {
                  color: #718096;
                }

                &:hover .mat-mdc-select-trigger {
                  border-color: #cbd5e0;
                  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                }
              }
            }
          }
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          text-align: center;
          color: #666;

          .empty-icon {
            font-size: 64px;
            width: 64px;
            height: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
          }

          h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
          }

          p {
            margin: 0 0 24px 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

// ===== GLOBAL TABLE THEME OVERRIDES =====
::ng-deep {
  // Override Material Table default colors globally for this component
  .smart-dashboard-container {
    .mat-mdc-table {
      .mat-mdc-header-row {
        height: auto !important;
        min-height: 40px !important;

        .mat-mdc-header-cell {
          color: #495057 !important;
          background: #f8f9fa !important;
          font-weight: 600 !important;
          font-size: 11px !important;
          text-align: center !important;
          vertical-align: top !important;
          white-space: normal !important;
          overflow: visible !important;
          text-overflow: clip !important;
          height: auto !important;
          min-height: 40px !important;
          line-height: 1.2 !important;
          word-wrap: break-word !important;
          hyphens: auto !important;
          padding: 10px 6px !important;
          display: table-cell !important;

          &:first-child {
            text-align: left !important;
            color: #343a40 !important;
            padding-left: 14px !important;
            padding-right: 6px !important;
          }
        }
      }

      // Disable all table row hover effects
      .mat-mdc-row {
        &:hover {
          background: transparent !important;
        }
      }
    }

      .mat-mdc-row {
        .mat-mdc-cell {
          border-bottom-color: #f1f3f4 !important;
          text-align: center !important;
          vertical-align: middle !important;

          &:first-child {
            text-align: left !important;
          }
        }
      }
    }

    // Override Material Paginator colors
    .mat-mdc-paginator {
      .mat-mdc-paginator-range-actions {
        .mat-mdc-icon-button {
          color: #ffb366 !important;

          &:hover {
            background: rgba(255, 179, 102, 0.1) !important;
          }

          &[disabled] {
            color: #adb5bd !important;
          }
        }
      }
    }

    // Table cell content styling
    .category-cell {
      font-weight: 600 !important;
      color: #d67e2a !important;
    }

    .grand-total-cell {
      font-weight: 700 !important;
      color: #0056b3 !important;
    }

    // Enable horizontal scrolling for responsive tables
    .table-responsive {
      overflow-x: auto !important;
      overflow-y: visible !important;

      // Custom scrollbar styling for horizontal scroll
      &::-webkit-scrollbar {
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;

        &:hover {
          background: #a8a8a8;
        }
      }

      // Firefox scrollbar
      scrollbar-width: thin;
      scrollbar-color: #c1c1c1 #f1f1f1;
    }

    // Ensure proper table layout
    .reconciliation-mat-table {
      table-layout: fixed !important;
      width: 100% !important;
      border-collapse: separate !important;
      border-spacing: 0 !important;
    }

    // Enable horizontal scrolling for table charts
    .table-chart .table-responsive {
      overflow-x: auto !important;
      overflow-y: visible !important;
    }
  }

// Responsive Design
@media (max-width: 1600px) {
  .smart-dashboard-container .dashboard-grid {
    .summary-cards-row {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .charts-grid {
      .chart-card.two-thirds-width {
        grid-column: span 12;
      }

      .chart-card.quarter-width {
        grid-column: span 6;
      }
    }
  }
}

@media (max-width: 1400px) {
  .smart-dashboard-container .dashboard-grid {
    .summary-cards-row {
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }

    .charts-grid {
      .chart-card.half-width,
      .chart-card.third-width,
      .chart-card.quarter-width,
      .chart-card.two-thirds-width {
        grid-column: span 12;
      }
    }
  }
}

@media (max-width: 1024px) {
  .smart-dashboard-container {
    .ai-assistant-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .left-sidebar {
      width: 220px;
    }
  }
}

@media (max-width: 768px) {
  .smart-dashboard-container .main-layout {
    flex-direction: column;

    .left-sidebar {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #e9ecef;
      padding: 12px;

      // Mobile dashboard mode section
      .dashboard-mode-section {
        position: relative;
        margin-top: 16px;
        border-top: 1px solid #e9ecef;
        border-radius: 8px;
        background: #f8f9fa;

        .mode-toggle-container {
          .toggle-switch {
            .toggle-option {
              padding: 10px 16px;
              font-size: 13px;
            }
          }
        }
      }
    }

    .right-content {
      .ai-assistant-header {
        flex-direction: column;
        gap: 12px;
        padding: 12px 16px;
      }

      .dashboard-content-area {
        padding: 12px;

        .dashboard-grid {
          .dashboard-section {
            margin-bottom: 24px;

            .section-header {
              padding: 8px 0;
              margin-bottom: 12px;

              .section-title {
                font-size: 16px;
              }

              .section-subtitle {
                font-size: 12px;
              }
            }
          }

          .summary-cards-row {
            grid-template-columns: 1fr;
            gap: 12px;
          }

          .charts-grid {
            gap: 12px;

            .chart-card {
              grid-column: span 12;

              .chart-title {
                font-size: 14px;
                padding: 12px 16px 6px 16px;
              }
            }
          }
        }

        // Mobile Table Styling
        .table-chart {
          .table-responsive {
            border-radius: 8px;
            margin: 0;

            .table-controls {
              padding: 12px 16px;
              flex-direction: column;
              gap: 12px;
              align-items: stretch;

              .table-search {
                max-width: none;
              }

              .table-export {
                align-self: flex-end;
              }
            }

            .reconciliation-mat-table {
              .mat-mdc-header-row {
                height: 40px;

                .mat-mdc-header-cell {
                  padding: 8px 12px;
                  font-size: 11px;

                  &:first-child {
                    padding-left: 16px;
                  }
                }
              }

              .mat-mdc-row {
                height: 40px;

                .mat-mdc-cell {
                  padding: 8px 12px;
                  font-size: 12px;

                  &:first-child {
                    padding-left: 16px;
                  }
                }

                &.subcategory-row .mat-mdc-cell:first-child {
                  padding-left: 24px;
                }
              }
            }

            .mat-mdc-paginator {
              .mat-mdc-paginator-container {
                padding: 8px 12px;
                min-height: 40px;
              }

              .mat-mdc-paginator-page-size,
              .mat-mdc-paginator-range-label {
                font-size: 11px;
              }

              .mat-mdc-icon-button {
                width: 28px;
                height: 28px;
              }
            }
          }
        }
      }
    }
  }

  // Responsive Design for Loading State
  @media (max-width: 768px) {
    .loading-container {
      margin: 10px;
      padding: 20px 10px;
      min-height: 350px;

      .loading-content {
        gap: 24px;

        .loading-animation {
          width: 80px;
          height: 80px;

          .loading-icon {
            font-size: 32px;
            width: 32px;
            height: 32px;
          }
        }

        .loading-text {
          h3 {
            font-size: 20px;
          }

          p {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// Select All custom option styling
::ng-deep .select-all-custom-option {
  background-color: #f5f5f5 !important;
  padding: 0 16px;
  height: 32px;
  line-height: 32px;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;



  strong {
    color: #1976d2 !important;
    font-weight: 500 !important;
  }
}

// ===== DEPARTMENT MAPPING STYLES =====

.department-mapping-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.department-mapping-container {
  background: white;
  border-radius: 4px;
  width: 95%;
  max-width: 1100px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, #ffb366 0%, #ff9f43 100%);
  color: white;
  flex-shrink: 0;

  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 10px;

    .mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }

  button {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    width: 32px;
    height: 32px;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .mat-icon {
      font-size: 18px;
    }
  }
}

.mapping-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 0;
  min-height: 0;
}

.department-selection-section {
  padding: 6px;
  border-right: 1px solid #e9ecef;
  background: #f8f9fa;

  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;

    .mat-icon {
      color: #ffb366;
      font-size: 20px;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;

    mat-spinner {
      margin-bottom: 16px;

      ::ng-deep circle {
        stroke: #ffb366;
      }
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .department-filter-field {
    width: 100%;

    ::ng-deep .mat-mdc-text-field-wrapper {
      background-color: white;
    }
  }
}

.category-mapping-section {
  padding: 16px;
  overflow: visible;
  flex: 1;
  display: flex;
  flex-direction: column;

  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    flex-shrink: 0;

    .mat-icon {
      color: #ffb366;
      font-size: 20px;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  app-department-category-mapping {
    display: block;
    flex: 1;
    overflow: visible;
  }
}

// ===== MAPPING WARNING STYLES =====
.mapping-warning-container {
  margin: 16px 24px;

  .mapping-warning {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffc107;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.15);

    .warning-icon {
      color: #ff9800;
      font-size: 28px;
      width: 28px;
      height: 28px;
      flex-shrink: 0;
    }

    .warning-content {
      flex: 1;

      h4 {
        margin: 0 0 8px 0;
        color: #e65100;
        font-size: 16px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #bf360c;
        font-size: 14px;
        line-height: 1.4;
      }
    }

    button {
      flex-shrink: 0;
      font-weight: 500;

      ::ng-deep {
        .mat-mdc-button-base {
          border-radius: 6px;
        }
      }
    }

    .warning-actions {
      display: flex;
      gap: 8px;
      flex-shrink: 0;

      button {
        margin: 0;
        min-width: 100px;
        font-weight: 500;

        ::ng-deep {
          .mat-mdc-button-base {
            border-radius: 6px;
          }
        }
      }
    }
  }
}

// ===== MAPPING REQUIRED BUTTON STYLES =====
.filter-group {
  .mapping-required {
    border-color: #ff9800 !important;
    color: #ff9800 !important;
    background-color: rgba(255, 152, 0, 0.05) !important;
    animation: pulse-warning 2s infinite;

    mat-icon {
      color: #ff9800 !important;
    }

    &:hover {
      background-color: rgba(255, 152, 0, 0.1) !important;
      border-color: #f57c00 !important;
    }
  }

  .kitchen-required {
    border-color: #ff9800 !important;
    color: #ff9800 !important;
    background-color: rgba(255, 152, 0, 0.05) !important;
    animation: pulse-warning 2s infinite;

    mat-icon {
      color: #ff9800 !important;
    }

    &:hover {
      background-color: rgba(255, 152, 0, 0.1) !important;
      border-color: #f57c00 !important;
    }
  }
}

// ===== SUCCESS ICON STYLES =====
.success-icon {
  color: #4caf50 !important;
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
}

@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(255, 152, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
  }
}